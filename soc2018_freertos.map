Archive member included to satisfy reference by file (symbol)

F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
                              obj/main.o (__aeabi_uidiv)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
                              obj/main.o (__aeabi_idiv)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o) (__aeabi_idiv0)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
                              obj/BSPPrint.o (__aeabi_d2ulz)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o) (__aeabi_dmul)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o) (__aeabi_dsub)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o) (__aeabi_d2uiz)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
                              obj/tasks.o (memset)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
                              obj/tasks.o (sprintf)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
                              obj/tasks.o (stpcpy)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
                              obj/tasks.o (strlen)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o) (_svfprintf_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_dtoa_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_free_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o) (_impure_ptr)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_localeconv_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_malloc_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (memchr)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o) (__malloc_lock)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o) (_Balloc)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (frexp)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o) (_sbrk_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (strncpy)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__ssprint_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o) (_calloc_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o) (__global_locale)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o) (__retarget_lock_acquire_recursive)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (__ascii_mbtowc)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o) (memmove)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o) (_realloc_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o) (errno)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (strcmp)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (__ascii_wctomb)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (_ctype_)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o) (__aeabi_ddiv)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_dcmpeq)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_dcmpun)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_d2iz)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_uldivmod)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o) (__udivmoddi4)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o) (__clzdi2)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o) (__clzsi2)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o) (_sbrk)

Allocating common symbols
Common symbol       size              file

rdata               0x4               obj/gic_handle_irq.o
__lock___atexit_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___arc4random_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
StartTask_Handler   0x4               obj/main.o
errno               0x4               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
rlt                 0x1               obj/timer.o
fiq_num             0x4               obj/gic_handle_irq.o
xQueueRegistry      0x40              obj/queue.o
error_temp          0x4               obj/FreeRTOS_aux.o
__lock___env_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___sinit_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
irq_num             0x4               obj/gic_handle_irq.o
__lock___malloc_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___at_quick_exit_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___dd_hash_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___tz_mutex   0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
rdata2              0x4               obj/gic_handle_irq.o
__lock___sfp_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
rlt_tmp             0x1               obj/apb_timer.o

Discarded input sections

 .data          0x00000000        0x0 obj/lowlevel.o
 .bss           0x00000000        0x0 obj/lowlevel.o
 .text          0x00000000       0x38 obj/mem_clear.o
 .data          0x00000000        0x0 obj/mem_clear.o
 .bss           0x00000000        0x0 obj/mem_clear.o
 .debug_line    0x00000000       0x54 obj/mem_clear.o
 .debug_info    0x00000000       0x26 obj/mem_clear.o
 .debug_abbrev  0x00000000       0x14 obj/mem_clear.o
 .debug_aranges
                0x00000000       0x20 obj/mem_clear.o
 .debug_str     0x00000000       0x4b obj/mem_clear.o
 .ARM.attributes
                0x00000000       0x25 obj/mem_clear.o
 .data          0x00000000        0x0 obj/start.o
 .bss           0x00000000        0x0 obj/start.o
 .text          0x00000000        0x0 obj/vectors.o
 .data          0x00000000        0x0 obj/vectors.o
 .bss           0x00000000        0x0 obj/vectors.o
 .debug_line    0x00000000       0x7a obj/vectors.o
 .debug_info    0x00000000       0x26 obj/vectors.o
 .debug_abbrev  0x00000000       0x14 obj/vectors.o
 .debug_aranges
                0x00000000       0x20 obj/vectors.o
 .debug_str     0x00000000       0x49 obj/vectors.o
 .ARM.attributes
                0x00000000       0x25 obj/vectors.o
 .data          0x00000000        0x0 obj/portASM.o
 .bss           0x00000000        0x0 obj/portASM.o
 .text          0x00000000       0x50 obj/FreeRTOS_aux.o
 .data          0x00000000        0x0 obj/FreeRTOS_aux.o
 .bss           0x00000000        0x0 obj/FreeRTOS_aux.o
 .rodata.str1.4
                0x00000000       0x47 obj/FreeRTOS_aux.o
 .debug_info    0x00000000      0xaf9 obj/FreeRTOS_aux.o
 .debug_abbrev  0x00000000      0x2cd obj/FreeRTOS_aux.o
 .debug_loc     0x00000000       0x5a obj/FreeRTOS_aux.o
 .debug_aranges
                0x00000000       0x20 obj/FreeRTOS_aux.o
 .debug_ranges  0x00000000       0x28 obj/FreeRTOS_aux.o
 .debug_line    0x00000000      0x2a7 obj/FreeRTOS_aux.o
 .debug_str     0x00000000      0x641 obj/FreeRTOS_aux.o
 .comment       0x00000000       0x7a obj/FreeRTOS_aux.o
 .debug_frame   0x00000000       0x3c obj/FreeRTOS_aux.o
 .ARM.attributes
                0x00000000       0x37 obj/FreeRTOS_aux.o
 COMMON         0x00000000        0x4 obj/FreeRTOS_aux.o
 .data          0x00000000        0x0 obj/main.o
 .bss           0x00000000        0x8 obj/main.o
 .rodata        0x00000000    0x1c028 obj/main.o
 .text          0x00000000       0x20 obj/apb_timer.o
 .data          0x00000000        0x0 obj/apb_timer.o
 .bss           0x00000000        0x0 obj/apb_timer.o
 .debug_info    0x00000000       0xf3 obj/apb_timer.o
 .debug_abbrev  0x00000000       0xb8 obj/apb_timer.o
 .debug_aranges
                0x00000000       0x20 obj/apb_timer.o
 .debug_line    0x00000000       0x8e obj/apb_timer.o
 .debug_str     0x00000000      0x1b0 obj/apb_timer.o
 .comment       0x00000000       0x7a obj/apb_timer.o
 .debug_frame   0x00000000       0x20 obj/apb_timer.o
 .ARM.attributes
                0x00000000       0x37 obj/apb_timer.o
 COMMON         0x00000000        0x1 obj/apb_timer.o
 .data          0x00000000        0x0 obj/BSPPrint.o
 .bss           0x00000000        0x0 obj/BSPPrint.o
 .data          0x00000000        0x0 obj/gic_handle_irq.o
 .bss           0x00000000        0x4 obj/gic_handle_irq.o
 .text          0x00000000      0x2e8 obj/gic_irq.o
 .data          0x00000000        0x0 obj/gic_irq.o
 .bss           0x00000000        0x0 obj/gic_irq.o
 .rodata.str1.4
                0x00000000       0x30 obj/gic_irq.o
 .debug_info    0x00000000      0x933 obj/gic_irq.o
 .debug_abbrev  0x00000000      0x274 obj/gic_irq.o
 .debug_loc     0x00000000      0x7de obj/gic_irq.o
 .debug_aranges
                0x00000000       0x20 obj/gic_irq.o
 .debug_ranges  0x00000000      0x2b8 obj/gic_irq.o
 .debug_line    0x00000000      0x421 obj/gic_irq.o
 .debug_str     0x00000000      0x34e obj/gic_irq.o
 .comment       0x00000000       0x7a obj/gic_irq.o
 .debug_frame   0x00000000      0x140 obj/gic_irq.o
 .ARM.attributes
                0x00000000       0x37 obj/gic_irq.o
 .data          0x00000000        0x0 obj/irq_gic.o
 .data          0x00000000        0x0 obj/mprintf.o
 .bss           0x00000000        0x0 obj/mprintf.o
 .data          0x00000000        0x0 obj/sram_on_chip_test.o
 .bss           0x00000000        0x0 obj/sram_on_chip_test.o
 .data          0x00000000        0x0 obj/timer.o
 .bss           0x00000000        0x0 obj/timer.o
 .text          0x00000000       0xec obj/uart.o
 .data          0x00000000        0x0 obj/uart.o
 .bss           0x00000000        0x0 obj/uart.o
 .debug_info    0x00000000      0x29f obj/uart.o
 .debug_abbrev  0x00000000      0x1ad obj/uart.o
 .debug_loc     0x00000000      0x11d obj/uart.o
 .debug_aranges
                0x00000000       0x20 obj/uart.o
 .debug_ranges  0x00000000       0x18 obj/uart.o
 .debug_line    0x00000000      0x15a obj/uart.o
 .debug_str     0x00000000      0x1ff obj/uart.o
 .comment       0x00000000       0x7a obj/uart.o
 .debug_frame   0x00000000       0x70 obj/uart.o
 .ARM.attributes
                0x00000000       0x37 obj/uart.o
 .data          0x00000000        0x0 obj/list.o
 .bss           0x00000000        0x0 obj/list.o
 .text          0x00000000      0xce8 obj/queue.o
 .data          0x00000000        0x0 obj/queue.o
 .bss           0x00000000        0x0 obj/queue.o
 .debug_info    0x00000000     0x2457 obj/queue.o
 .debug_abbrev  0x00000000      0x45d obj/queue.o
 .debug_loc     0x00000000     0x13ba obj/queue.o
 .debug_aranges
                0x00000000       0x20 obj/queue.o
 .debug_ranges  0x00000000      0x240 obj/queue.o
 .debug_line    0x00000000     0x12c4 obj/queue.o
 .debug_str     0x00000000      0xd49 obj/queue.o
 .comment       0x00000000       0x7a obj/queue.o
 .debug_frame   0x00000000      0x2bc obj/queue.o
 .ARM.attributes
                0x00000000       0x37 obj/queue.o
 COMMON         0x00000000       0x40 obj/queue.o
 .data          0x00000000        0x0 obj/heap_4.o
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .text          0x00000000      0x290 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .debug_frame   0x00000000       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .text._sprintf_r
                0x00000000       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .rodata._global_impure_ptr
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .text.__localeconv_l
                0x00000000        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .text.localeconv
                0x00000000        0xc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__s2b    0x00000000       0xf0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__ulp    0x00000000       0x64 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__b2d    0x00000000       0xe0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__ratio  0x00000000       0x84 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text._mprec_log10
                0x00000000       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__copybits
                0x00000000       0x70 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__any_on
                0x00000000       0x64 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .rodata.__mprec_tinytens
                0x00000000       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .rodata._svfiprintf_r.str1.4
                0x00000000       0x2f F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .text._svfiprintf_r
                0x00000000     0x1114 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .rodata.blanks.7324
                0x00000000       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .rodata.zeroes.7325
                0x00000000       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text._setlocale_r
                0x00000000       0x68 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text.__locale_mb_cur_max
                0x00000000       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text.setlocale
                0x00000000       0x18 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .bss._PathLocale
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_init
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_init_recursive
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_close
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_close_recursive
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_acquire
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_try_acquire
                0x00000000        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_try_acquire_recursive
                0x00000000        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_release
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .text._mbtowc_r
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .text.cleanup_glue
                0x00000000       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .text._reclaim_reent
                0x00000000       0xf0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .text          0x00000000      0x224 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .debug_frame   0x00000000       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .ARM.attributes
                0x00000000       0x1a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .text._wctomb_r
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .ARM.extab     0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)

Memory Configuration

Name             Origin             Length             Attributes
RAM              0x40018000         0x08000000         xrw
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD obj/lowlevel.o
LOAD obj/mem_clear.o
LOAD obj/start.o
LOAD obj/vectors.o
LOAD obj/portASM.o
LOAD obj/FreeRTOS_aux.o
LOAD obj/main.o
LOAD obj/apb_timer.o
LOAD obj/BSPPrint.o
LOAD obj/gic_handle_irq.o
LOAD obj/gic_irq.o
LOAD obj/irq_gic.o
LOAD obj/mprintf.o
LOAD obj/sram_on_chip_test.o
LOAD obj/timer.o
LOAD obj/uart.o
LOAD obj/list.o
LOAD obj/queue.o
LOAD obj/tasks.o
LOAD obj/port.o
LOAD obj/heap_4.o
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libstdc++.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libm.a
START GROUP
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libg.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a
END GROUP
START GROUP
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a
END GROUP

.text           0x40018000     0xbde4
                0x40018000                __text_start__ = .
 obj/vectors.o(.vectors)
 .vectors       0x40018000      0x244 obj/vectors.o
                0x40018000                _start
                0x40018020                _undefined_instruction
                0x40018024                _software_interrupt
                0x40018028                _prefetch_abort
                0x4001802c                _data_abort
                0x40018030                _not_used
                0x40018034                _irq
                0x40018038                _fiq
                0x40018040                IRQ_STACK_START_IN
                0x40018044                FIQ_STACK_START_IN
 *(.text*)
 *fill*         0x40018244        0xc 
 .text          0x40018250      0x2a0 obj/lowlevel.o
                0x40018250                lowlevel_init
                0x400182b8                stack_test
                0x400182cc                stop_sim
                0x400182d8                memcpy
                0x400182f4                uart_init
                0x400182f8                uart_out
                0x40018304                puts
                0x40018328                putnum
                0x40018400                newline
                0x4001841c                readl
 .text          0x400184f0       0x9c obj/start.o
                0x400184f0                reset
                0x40018530                c_runtime_cpu_setup
                0x40018548                save_boot_params
                0x4001854c                cpu_init_cp15
                0x40018584                cpu_init_crit
 *fill*         0x4001858c        0x4 
 .text          0x40018590      0x2e0 obj/portASM.o
                0x40018590                FreeRTOS_SWI_Handler
                0x40018638                vPortRestoreTaskContext
                0x400186a0                FreeRTOS_IRQ_Handler
                0x40018810                vApplicationIRQHandler
 .text          0x40018870      0x45c obj/main.o
                0x40018870                start_task
                0x40018890                printDouble
                0x40018a18                _write
                0x40018a50                MoniterRead
                0x40018a68                freertos_start
                0x40018aa8                c_main
                0x40018b14                apb_uart_init
                0x40018b6c                outbyte
                0x40018b98                SYS_Delay
                0x40018bdc                get_tb_start
                0x40018c08                get_tb_end
                0x40018c18                get_tb_diff
                0x40018c20                delayus
                0x40018c74                delay_ms
 .text          0x40018ccc      0x2d0 obj/BSPPrint.o
                0x40018d78                print2
                0x40018f84                pchar
 .text          0x40018f9c      0x308 obj/gic_handle_irq.o
                0x40018f9c                gic_handle_fiq
                0x40019170                apb_timer1_int_service
                0x400191dc                do_undefined_instruction
                0x40019204                do_software_interrupt
                0x40019240                do_prefetch_abort
                0x40019280                do_data_abort
                0x40019298                do_not_used
 .text          0x400192a4      0x388 obj/irq_gic.o
                0x400192a4                gic_get_irq_number
                0x400192b8                gic_enable_irq
                0x400192dc                gic_disable_irq
                0x40019300                gic_eoi_irq
                0x40019310                gic_send_sgi
                0x40019328                gic_global_enable
                0x4001933c                gic_global_disable
                0x40019370                gic_dist_config
                0x4001945c                gic_configure_irq
                0x400194d0                gic_set_type
                0x400194e8                gic_handle_irq_init
                0x400194f4                gic_register_irq_entry
                0x40019508                gic_remove_irq_entry
                0x40019520                gic_handle_irq
                0x40019560                gic_dist_init
                0x400195a8                gic_cpu_init
                0x400195c4                gic_cpu_config
                0x400195c8                disable_irq
                0x400195d8                enable_irq
                0x400195e8                gic_init
                0x40019624                SWI_Enable
 .text          0x4001962c      0x67c obj/mprintf.o
                0x400197a0                printnum
                0x400197d4                print
                0x400197fc                isdigit_m
                0x40019810                tolower_m
                0x40019820                strlen_m
                0x40019848                mprintf
                0x40019c24                printreg
                0x40019c70                printval
 .text          0x40019ca8      0xac8 obj/sram_on_chip_test.o
                0x40019ca8                sram_dancuo_irq_handler
                0x40019cfc                sram_duocuo_irq_handler
                0x40019d84                sram_on_chip_regtest
                0x40019e14                sram_on_chip_rw_test
                0x40019eec                sram_on_chip_bypass_test
                0x40019ff0                sram_on_chip_dancuo_test
                0x4001a4a4                sram_on_chip_duocuo_test
 .text          0x4001a770      0x1ac obj/timer.o
                0x4001a770                private_timer_handle_irq
                0x4001a798                global_timer_handle_irq
                0x4001a7e8                timer_A9_test
                0x4001a8b8                vClearTickInterrupt
                0x4001a8cc                vConfigureTickInterrupt
 .text          0x4001a91c       0xec obj/list.o
                0x4001a91c                vListInitialise
                0x4001a940                vListInitialiseItem
                0x4001a94c                vListInsertEnd
                0x4001a978                vListInsert
                0x4001a9d0                uxListRemove
 .text          0x4001aa08     0x18b8 obj/tasks.o
                0x4001ade8                xTaskCreate
                0x4001b060                vTaskDelete
                0x4001b16c                eTaskGetState
                0x4001b18c                uxTaskPriorityGet
                0x4001b1b8                uxTaskPriorityGetFromISR
                0x4001b1e4                vTaskPrioritySet
                0x4001b314                vTaskSuspend
                0x4001b41c                vTaskResume
                0x4001b4ec                xTaskResumeFromISR
                0x4001b5e4                vTaskStartScheduler
                0x4001b65c                vTaskEndScheduler
                0x4001b67c                vTaskSuspendAll
                0x4001b694                xTaskResumeAll
                0x4001b844                xTaskDelayUntil
                0x4001b8e4                vTaskDelay
                0x4001b928                xTaskGetTickCount
                0x4001b938                xTaskGetTickCountFromISR
                0x4001b948                uxTaskGetNumberOfTasks
                0x4001b958                pcTaskGetName
                0x4001b970                xTaskCatchUpTicks
                0x4001b9a8                xTaskIncrementTick
                0x4001b9d4                vTaskSetApplicationTaskTag
                0x4001b9fc                xTaskGetApplicationTaskTag
                0x4001ba24                xTaskGetApplicationTaskTagFromISR
                0x4001ba4c                xTaskCallApplicationTaskHook
                0x4001ba78                vTaskSwitchContext
                0x4001ba9c                vTaskPlaceOnEventList
                0x4001bac8                vTaskPlaceOnUnorderedEventList
                0x4001bb3c                xTaskRemoveFromEventList
                0x4001bc74                vTaskRemoveFromUnorderedEventList
                0x4001bd60                vTaskSetTimeOutState
                0x4001bd88                vTaskInternalSetTimeOutState
                0x4001bda0                xTaskCheckForTimeOut
                0x4001be30                vTaskMissedYield
                0x4001be44                uxTaskGetTaskNumber
                0x4001be50                vTaskSetTaskNumber
                0x4001be5c                vTaskSetThreadLocalStoragePointer
                0x4001be80                pvTaskGetThreadLocalStoragePointer
                0x4001beac                vTaskGetInfo
                0x4001c018                uxTaskGetSystemState
                0x4001c14c                xTaskGetCurrentTaskHandle
                0x4001c15c                xTaskGetSchedulerState
                0x4001c18c                vTaskList
                0x4001c298                uxTaskResetEventItemValue
 .text          0x4001c2c0      0x350 obj/port.o
                0x4001c300                pxPortInitialiseStack
                0x4001c3dc                xPortStartScheduler
                0x4001c42c                vPortEndScheduler
                0x4001c430                vPortEnterCritical
                0x4001c488                vPortExitCritical
                0x4001c4e8                FreeRTOS_Tick_Handler
                0x4001c56c                vPortTaskUsesFPU
                0x4001c588                vPortClearInterruptMask
                0x4001c5c4                ulPortSetInterruptMask
                0x4001c60c                vApplicationFPUSafeIRQHandler
 .text          0x4001c610      0x348 obj/heap_4.o
                0x4001c68c                pvPortMalloc
                0x4001c7ec                vPortFree
                0x4001c850                xPortGetFreeHeapSize
                0x4001c860                xPortGetMinimumEverFreeHeapSize
                0x4001c870                vPortInitialiseBlocks
                0x4001c874                pvPortCalloc
                0x4001c8b8                vPortGetHeapStats
 .text          0x4001c958      0x114 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
                0x4001c958                __aeabi_uidiv
                0x4001c958                __udivsi3
                0x4001ca4c                __aeabi_uidivmod
 .text          0x4001ca6c      0x148 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
                0x4001ca6c                __divsi3
                0x4001ca6c                __aeabi_idiv
                0x4001cb94                __aeabi_idivmod
 .text          0x4001cbb4        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
                0x4001cbb4                __aeabi_idiv0
                0x4001cbb4                __aeabi_ldiv0
 .text          0x4001cbb8       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
                0x4001cbb8                __fixunsdfdi
                0x4001cbb8                __aeabi_d2ulz
 .text          0x4001cc14      0x424 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
                0x4001cc14                __aeabi_drsub
                0x4001cc1c                __aeabi_dsub
                0x4001cc1c                __subdf3
                0x4001cc20                __aeabi_dadd
                0x4001cc20                __adddf3
                0x4001cf30                __floatunsidf
                0x4001cf30                __aeabi_ui2d
                0x4001cf54                __floatsidf
                0x4001cf54                __aeabi_i2d
                0x4001cf7c                __aeabi_f2d
                0x4001cf7c                __extendsfdf2
                0x4001cfc4                __floatundidf
                0x4001cfc4                __aeabi_ul2d
                0x4001cfd8                __floatdidf
                0x4001cfd8                __aeabi_l2d
 .text          0x4001d038       0x54 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
                0x4001d038                __aeabi_d2uiz
                0x4001d038                __fixunsdfsi
 .text.memset   0x4001d08c      0x11c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
                0x4001d08c                memset
 .text.sprintf  0x4001d1a8       0x6c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
                0x4001d1a8                sprintf
 .text.stpcpy   0x4001d214       0x74 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
                0x4001d214                stpcpy
 .text.strlen   0x4001d288       0x60 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
                0x4001d288                strlen
 .text._svfprintf_r
                0x4001d2e8     0x27a0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
                0x4001d2e8                _svfprintf_r
 .text.quorem   0x4001fa88      0x1c0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .text._dtoa_r  0x4001fc48     0x16d4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
                0x4001fc48                _dtoa_r
 .text._malloc_trim_r
                0x4002131c      0x100 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
                0x4002131c                _malloc_trim_r
 .text._free_r  0x4002141c      0x2ec F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
                0x4002141c                _free_r
 .text._localeconv_r
                0x40021708        0xc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
                0x40021708                _localeconv_r
 .text._malloc_r
                0x40021714      0x7d4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40021714                _malloc_r
 .text.memchr   0x40021ee8       0xf8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
                0x40021ee8                memchr
 .text.__malloc_lock
                0x40021fe0       0x18 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
                0x40021fe0                __malloc_lock
 .text.__malloc_unlock
                0x40021ff8       0x18 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
                0x40021ff8                __malloc_unlock
 .text._Balloc  0x40022010       0x8c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40022010                _Balloc
 .text._Bfree   0x4002209c       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x4002209c                _Bfree
 .text.__multadd
                0x400220b8       0xd0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x400220b8                __multadd
 .text.__hi0bits
                0x40022188       0x58 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40022188                __hi0bits
 .text.__lo0bits
                0x400221e0       0xa0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x400221e0                __lo0bits
 .text.__i2b    0x40022280       0x24 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40022280                __i2b
 .text.__multiply
                0x400222a4      0x1f0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x400222a4                __multiply
 .text.__pow5mult
                0x40022494      0x104 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40022494                __pow5mult
 .text.__lshift
                0x40022598      0x118 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40022598                __lshift
 .text.__mcmp   0x400226b0       0x60 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x400226b0                __mcmp
 .text.__mdiff  0x40022710      0x1e4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40022710                __mdiff
 .text.__d2b    0x400228f4      0x118 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x400228f4                __d2b
 .text.frexp    0x40022a0c       0xa4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
                0x40022a0c                frexp
 .text._sbrk_r  0x40022ab0       0x44 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
                0x40022ab0                _sbrk_r
 .text.strncpy  0x40022af4       0xcc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
                0x40022af4                strncpy
 .text.__ssprint_r
                0x40022bc0      0x1a4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
                0x40022bc0                __ssprint_r
 .text._calloc_r
                0x40022d64       0x9c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
                0x40022d64                _calloc_r
 .text.__retarget_lock_acquire_recursive
                0x40022e00        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
                0x40022e00                __retarget_lock_acquire_recursive
 .text.__retarget_lock_release_recursive
                0x40022e04        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
                0x40022e04                __retarget_lock_release_recursive
 .text.__ascii_mbtowc
                0x40022e08       0x44 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
                0x40022e08                __ascii_mbtowc
 .text.memmove  0x40022e4c      0x158 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
                0x40022e4c                memmove
 .text._realloc_r
                0x40022fa4      0x594 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
                0x40022fa4                _realloc_r
 .text.__ascii_wctomb
                0x40023538       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
                0x40023538                __ascii_wctomb
 .text          0x40023568      0x49c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
                0x40023568                __aeabi_dmul
                0x40023568                __muldf3
                0x400237f8                __divdf3
                0x400237f8                __aeabi_ddiv
 .text          0x40023a04      0x144 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
                0x40023a04                __gtdf2
                0x40023a04                __gedf2
                0x40023a0c                __ltdf2
                0x40023a0c                __ledf2
                0x40023a14                __nedf2
                0x40023a14                __eqdf2
                0x40023a14                __cmpdf2
                0x40023a9c                __aeabi_cdrcmple
                0x40023ab8                __aeabi_cdcmpeq
                0x40023ab8                __aeabi_cdcmple
                0x40023ad0                __aeabi_dcmpeq
                0x40023ae8                __aeabi_dcmplt
                0x40023b00                __aeabi_dcmple
                0x40023b18                __aeabi_dcmpge
                0x40023b30                __aeabi_dcmpgt
 .text          0x40023b48       0x38 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
                0x40023b48                __unorddf2
                0x40023b48                __aeabi_dcmpun
 .text          0x40023b80       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
                0x40023b80                __aeabi_d2iz
                0x40023b80                __fixdfsi
 .text          0x40023bdc       0x3c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
                0x40023bdc                __aeabi_uldivmod
 .text          0x40023c18      0x130 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
                0x40023c18                __udivmoddi4
 .text          0x40023d48       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
                0x40023d48                __clzdi2
 .text          0x40023d70       0x48 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
                0x40023d70                __clzsi2
 .text._sbrk    0x40023db8       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
                0x40023db8                _sbrk
 *(.glue_7)
 .glue_7        0x40023de4        0x0 linker stubs
 *(.glue_7t)
 .glue_7t       0x40023de4        0x0 linker stubs
 *(.vfp11_veneer)
 .vfp11_veneer  0x40023de4        0x0 linker stubs
 *(.v4_bx)
 .v4_bx         0x40023de4        0x0 linker stubs
                0x40023de4                __text_end__ = .

.iplt           0x40023de4        0x0
 .iplt          0x40023de4        0x0 obj/lowlevel.o

.rodata         0x40023de8      0xc85
                0x40023de8                __rodata_start__ = .
 *(.rodata*)
 .rodata.str1.4
                0x40023de8       0x54 obj/main.o
 .rodata        0x40023e3c       0x10 obj/BSPPrint.o
 .rodata.str1.4
                0x40023e4c      0x32a obj/gic_handle_irq.o
 *fill*         0x40024176        0x2 
 .rodata.str1.4
                0x40024178       0x27 obj/irq_gic.o
 *fill*         0x4002419f        0x1 
 .rodata.str1.4
                0x400241a0       0x26 obj/mprintf.o
                                 0x2a (size before relaxing)
 *fill*         0x400241c6        0x2 
 .rodata.str1.4
                0x400241c8      0x5ff obj/sram_on_chip_test.o
 *fill*         0x400247c7        0x1 
 .rodata        0x400247c8       0x10 obj/sram_on_chip_test.o
 .rodata.str1.4
                0x400247d8       0x17 obj/tasks.o
 *fill*         0x400247ef        0x1 
 .rodata        0x400247f0        0x5 obj/tasks.o
 *fill*         0x400247f5        0x3 
 .rodata        0x400247f8       0x10 obj/port.o
                0x400247f8                ulMaxAPIPriorityMask
                0x400247fc                ulICCPMR
                0x40024800                ulICCEOIR
                0x40024804                ulICCIAR
 .rodata._svfprintf_r.str1.4
                0x40024808       0x2b F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
                                 0x42 (size before relaxing)
 *fill*         0x40024833        0x1 
 .rodata.blanks.7345
                0x40024834       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .rodata.zeroes.7346
                0x40024844       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .rodata._dtoa_r.str1.4
                0x40024854        0xd F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
                                 0x12 (size before relaxing)
 *fill*         0x40024861        0x7 
 .rodata.__mprec_bigtens
                0x40024868       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40024868                __mprec_bigtens
 .rodata.__mprec_tens
                0x40024890       0xc8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                0x40024890                __mprec_tens
 .rodata.p05.6115
                0x40024958        0xc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .rodata._setlocale_r.str1.4
                0x40024964        0x6 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
                                  0xd (size before relaxing)
 .rodata.str1.4
                0x4002496a        0x2 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 *fill*         0x4002496a        0x2 
 .rodata._ctype_
                0x4002496c      0x101 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
                0x4002496c                _ctype_
                0x40024a6d                __rodata_end__ = .

.ARM.exidx      0x40024a70        0x8
 .ARM.exidx     0x40024a70        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)

.rel.dyn        0x40024a78        0x0
 .rel.iplt      0x40024a78        0x0 obj/lowlevel.o

.data           0x40024a78      0x9b4
                0x40024a78                __data_start__ = .
 *(.data*)
 .data          0x40024a78        0x4 obj/tasks.o
                0x40024a78                uxTopUsedPriority
 .data          0x40024a7c        0x4 obj/port.o
                0x40024a7c                ulCriticalNesting
 .data._impure_ptr
                0x40024a80        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
                0x40024a80                _impure_ptr
 *fill*         0x40024a84        0x4 
 .data.impure_data
                0x40024a88      0x428 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .data.__malloc_av_
                0x40024eb0      0x408 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40024eb0                __malloc_av_
 .data.__malloc_sbrk_base
                0x400252b8        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x400252b8                __malloc_sbrk_base
 .data.__malloc_trim_threshold
                0x400252bc        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x400252bc                __malloc_trim_threshold
 .data.__global_locale
                0x400252c0      0x16c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
                0x400252c0                __global_locale
                0x4002542c                __data_end__ = .
                0x4002542c                _case_list_start = .

.igot.plt       0x4002542c        0x0
 .igot.plt      0x4002542c        0x0 obj/lowlevel.o

.test_case
 *(.test_case)
                0x4002542c                _case_list_end = .

.bss            0x4002542c     0xcc24
                0x4002542c                __bss_start__ = .
 *(.bss*)
 .bss           0x4002542c      0x280 obj/irq_gic.o
 .bss           0x400256ac      0x104 obj/tasks.o
                0x400256cc                pxCurrentTCB
 .bss           0x400257b0        0xc obj/port.o
                0x400257b0                ulPortYieldRequired
                0x400257b4                ulPortTaskHasFPUContext
                0x400257b8                ulPortInterruptNesting
 .bss           0x400257bc     0xc81c obj/heap_4.o
 .bss.__malloc_current_mallinfo
                0x40031fd8       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40031fd8                __malloc_current_mallinfo
 .bss.__malloc_max_sbrked_mem
                0x40032000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40032000                __malloc_max_sbrked_mem
 .bss.__malloc_max_total_mem
                0x40032004        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40032004                __malloc_max_total_mem
 .bss.__malloc_top_pad
                0x40032008        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                0x40032008                __malloc_top_pad
 .bss.heap_end.4144
                0x4003200c        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
 *(COMMON)
 COMMON         0x40032010        0x4 obj/main.o
                0x40032010                StartTask_Handler
 COMMON         0x40032014       0x10 obj/gic_handle_irq.o
                0x40032014                rdata
                0x40032018                fiq_num
                0x4003201c                irq_num
                0x40032020                rdata2
 COMMON         0x40032024        0x1 obj/timer.o
                0x40032024                rlt
 *fill*         0x40032025        0x3 
 COMMON         0x40032028       0x21 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
                0x40032028                __lock___atexit_recursive_mutex
                0x4003202c                __lock___arc4random_mutex
                0x40032030                __lock___env_recursive_mutex
                0x40032034                __lock___sinit_recursive_mutex
                0x40032038                __lock___malloc_recursive_mutex
                0x4003203c                __lock___at_quick_exit_mutex
                0x40032040                __lock___dd_hash_mutex
                0x40032044                __lock___tz_mutex
                0x40032048                __lock___sfp_recursive_mutex
 *fill*         0x40032049        0x3 
 COMMON         0x4003204c        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
                0x4003204c                errno
                0x40032050                __bss_end__ = .

.heap           0x40032050   0x100000
                0x40032050                __heap_start__ = .
                0x40032050                __end__ = .
                0x40032050                PROVIDE (end = .)
 *(.heap*)
                0x40132050                . = (. + 0x100000)
 *fill*         0x40032050   0x100000 
                0x40132050                __heap_end__ = .
                0x40132050                __HeapLimit = __heap_end__

.stack          0x40132050    0x10000
                0x40132050                __stack_start__ = .
                0x40142050                . = (. + 0x10000)
 *fill*         0x40132050    0x10000 
                0x40142050                __stack_end__ = .
                0x40142050                __StackTop = __stack_end__

.debug_info     0x00000000     0x9f59
 *(.debug_info)
 .debug_info    0x00000000       0x26 obj/lowlevel.o
 .debug_info    0x00000026       0x26 obj/start.o
 .debug_info    0x0000004c       0x26 obj/portASM.o
 .debug_info    0x00000072     0x10ae obj/main.o
 .debug_info    0x00001120      0x4f8 obj/BSPPrint.o
 .debug_info    0x00001618      0x506 obj/gic_handle_irq.o
 .debug_info    0x00001b1e      0x683 obj/irq_gic.o
 .debug_info    0x000021a1     0x12b3 obj/mprintf.o
 .debug_info    0x00003454      0xd01 obj/sram_on_chip_test.o
 .debug_info    0x00004155      0x336 obj/timer.o
 .debug_info    0x0000448b      0xbe4 obj/list.o
 .debug_info    0x0000506f     0x3325 obj/tasks.o
 .debug_info    0x00008394      0xccd obj/port.o
 .debug_info    0x00009061      0xef8 obj/heap_4.o

.debug_abbrev   0x00000000     0x2359
 *(.debug_abbrev)
 .debug_abbrev  0x00000000       0x14 obj/lowlevel.o
 .debug_abbrev  0x00000014       0x14 obj/start.o
 .debug_abbrev  0x00000028       0x14 obj/portASM.o
 .debug_abbrev  0x0000003c      0x4a5 obj/main.o
 .debug_abbrev  0x000004e1      0x278 obj/BSPPrint.o
 .debug_abbrev  0x00000759      0x18b obj/gic_handle_irq.o
 .debug_abbrev  0x000008e4      0x368 obj/irq_gic.o
 .debug_abbrev  0x00000c4c      0x43f obj/mprintf.o
 .debug_abbrev  0x0000108b      0x20f obj/sram_on_chip_test.o
 .debug_abbrev  0x0000129a      0x19f obj/timer.o
 .debug_abbrev  0x00001439      0x26b obj/list.o
 .debug_abbrev  0x000016a4      0x58c obj/tasks.o
 .debug_abbrev  0x00001c30      0x34d obj/port.o
 .debug_abbrev  0x00001f7d      0x3dc obj/heap_4.o

.debug_line     0x00000000     0x548c
 *(.debug_line)
 .debug_line    0x00000000       0xdd obj/lowlevel.o
 .debug_line    0x000000dd       0x76 obj/start.o
 .debug_line    0x00000153       0xaf obj/portASM.o
 .debug_line    0x00000202      0x6dd obj/main.o
 .debug_line    0x000008df      0x416 obj/BSPPrint.o
 .debug_line    0x00000cf5      0x2f1 obj/gic_handle_irq.o
 .debug_line    0x00000fe6      0x4e6 obj/irq_gic.o
 .debug_line    0x000014cc      0x6c1 obj/mprintf.o
 .debug_line    0x00001b8d      0xb6e obj/sram_on_chip_test.o
 .debug_line    0x000026fb      0x282 obj/timer.o
 .debug_line    0x0000297d      0x3f6 obj/list.o
 .debug_line    0x00002d73     0x1aec obj/tasks.o
 .debug_line    0x0000485f      0x57a obj/port.o
 .debug_line    0x00004dd9      0x6b3 obj/heap_4.o

.debug_frame    0x00000000     0x1cc4
 *(.debug_frame)
 .debug_frame   0x00000000      0x164 obj/main.o
 .debug_frame   0x00000164       0x94 obj/BSPPrint.o
 .debug_frame   0x000001f8       0xd4 obj/gic_handle_irq.o
 .debug_frame   0x000002cc      0x188 obj/irq_gic.o
 .debug_frame   0x00000454      0x158 obj/mprintf.o
 .debug_frame   0x000005ac      0x118 obj/sram_on_chip_test.o
 .debug_frame   0x000006c4       0x78 obj/timer.o
 .debug_frame   0x0000073c       0x68 obj/list.o
 .debug_frame   0x000007a4      0x554 obj/tasks.o
 .debug_frame   0x00000cf8       0xe0 obj/port.o
 .debug_frame   0x00000dd8       0xe4 obj/heap_4.o
 .debug_frame   0x00000ebc       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .debug_frame   0x00000edc       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .debug_frame   0x00000efc       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .debug_frame   0x00000f30       0xac F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .debug_frame   0x00000fdc       0x24 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .debug_frame   0x00001000       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .debug_frame   0x00001050       0x6c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .debug_frame   0x000010bc       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .debug_frame   0x000010e8       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .debug_frame   0x00001108       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .debug_frame   0x00001158       0xc0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .debug_frame   0x00001218       0x74 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .debug_frame   0x0000128c       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .debug_frame   0x000012cc       0x74 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .debug_frame   0x00001340       0x4c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .debug_frame   0x0000138c       0x48 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .debug_frame   0x000013d4      0x2fc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .debug_frame   0x000016d0       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .debug_frame   0x00001704       0x3c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .debug_frame   0x00001740       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .debug_frame   0x00001780       0xa0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .debug_frame   0x00001820       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .debug_frame   0x00001854       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .debug_frame   0x000018a4       0xb0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .debug_frame   0x00001954       0x48 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .debug_frame   0x0000199c       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .debug_frame   0x000019d0       0x70 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .debug_frame   0x00001a40       0x64 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .debug_frame   0x00001aa4       0x3c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .debug_frame   0x00001ae0       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .debug_frame   0x00001b30       0xc4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .debug_frame   0x00001bf4       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .debug_frame   0x00001c14       0x24 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .debug_frame   0x00001c38       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .debug_frame   0x00001c64       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .debug_frame   0x00001ca4       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)

.debug_str      0x00000000     0x210a
 *(.debug_str)
 .debug_str     0x00000000       0x4a obj/lowlevel.o
 .debug_str     0x0000004a       0x14 obj/start.o
                                 0x47 (size before relaxing)
 .debug_str     0x0000005e       0x28 obj/portASM.o
                                 0x5b (size before relaxing)
 .debug_str     0x00000086      0x759 obj/main.o
                                0x7f8 (size before relaxing)
 .debug_str     0x000007df      0x209 obj/BSPPrint.o
                                0x360 (size before relaxing)
 .debug_str     0x000009e8      0x164 obj/gic_handle_irq.o
                                0x2e3 (size before relaxing)
 .debug_str     0x00000b4c      0x18c obj/irq_gic.o
                                0x373 (size before relaxing)
 .debug_str     0x00000cd8      0x111 obj/mprintf.o
                                0x6f7 (size before relaxing)
 .debug_str     0x00000de9       0xda obj/sram_on_chip_test.o
                                0x2cc (size before relaxing)
 .debug_str     0x00000ec3       0xb2 obj/timer.o
                                0x2cb (size before relaxing)
 .debug_str     0x00000f75      0x137 obj/list.o
                                0x6fd (size before relaxing)
 .debug_str     0x000010ac      0xc59 obj/tasks.o
                               0x13ec (size before relaxing)
 .debug_str     0x00001d05      0x14e obj/port.o
                                0x81d (size before relaxing)
 .debug_str     0x00001e53      0x2b7 obj/heap_4.o
                                0x921 (size before relaxing)

.debug_loc      0x00000000     0x43ba
 *(.debug_loc)
 .debug_loc     0x00000000      0x5d0 obj/main.o
 .debug_loc     0x000005d0      0x3d9 obj/BSPPrint.o
 .debug_loc     0x000009a9       0xba obj/gic_handle_irq.o
 .debug_loc     0x00000a63      0x472 obj/irq_gic.o
 .debug_loc     0x00000ed5      0xa8d obj/mprintf.o
 .debug_loc     0x00001962      0x587 obj/sram_on_chip_test.o
 .debug_loc     0x00001ee9       0x93 obj/list.o
 .debug_loc     0x00001f7c     0x1d13 obj/tasks.o
 .debug_loc     0x00003c8f      0x21b obj/port.o
 .debug_loc     0x00003eaa      0x510 obj/heap_4.o

.debug_aranges  0x00000000      0x1c0
 *(.debug_aranges)
 .debug_aranges
                0x00000000       0x20 obj/lowlevel.o
 .debug_aranges
                0x00000020       0x20 obj/start.o
 .debug_aranges
                0x00000040       0x20 obj/portASM.o
 .debug_aranges
                0x00000060       0x20 obj/main.o
 .debug_aranges
                0x00000080       0x20 obj/BSPPrint.o
 .debug_aranges
                0x000000a0       0x20 obj/gic_handle_irq.o
 .debug_aranges
                0x000000c0       0x20 obj/irq_gic.o
 .debug_aranges
                0x000000e0       0x20 obj/mprintf.o
 .debug_aranges
                0x00000100       0x20 obj/sram_on_chip_test.o
 .debug_aranges
                0x00000120       0x20 obj/timer.o
 .debug_aranges
                0x00000140       0x20 obj/list.o
 .debug_aranges
                0x00000160       0x20 obj/tasks.o
 .debug_aranges
                0x00000180       0x20 obj/port.o
 .debug_aranges
                0x000001a0       0x20 obj/heap_4.o

.debug_ranges   0x00000000      0x8f0
 *(.debug_ranges)
 .debug_ranges  0x00000000       0xc0 obj/main.o
 .debug_ranges  0x000000c0       0x88 obj/BSPPrint.o
 .debug_ranges  0x00000148       0x70 obj/irq_gic.o
 .debug_ranges  0x000001b8      0x120 obj/mprintf.o
 .debug_ranges  0x000002d8       0x48 obj/sram_on_chip_test.o
 .debug_ranges  0x00000320      0x5d0 obj/tasks.o

.debug_macinfo
 *(.debug_macinfo)

.comment        0x00000000       0x79
 *(.comment)
 .comment       0x00000000       0x79 obj/main.o
                                 0x7a (size before relaxing)
 .comment       0x00000079       0x7a obj/BSPPrint.o
 .comment       0x00000079       0x7a obj/gic_handle_irq.o
 .comment       0x00000079       0x7a obj/irq_gic.o
 .comment       0x00000079       0x7a obj/mprintf.o
 .comment       0x00000079       0x7a obj/sram_on_chip_test.o
 .comment       0x00000079       0x7a obj/timer.o
 .comment       0x00000079       0x7a obj/list.o
 .comment       0x00000079       0x7a obj/tasks.o
 .comment       0x00000079       0x7a obj/port.o
 .comment       0x00000079       0x7a obj/heap_4.o

.ARM.attributes
                0x00000000       0x33
 *(.ARM.attributes)
 .ARM.attributes
                0x00000000       0x25 obj/lowlevel.o
 .ARM.attributes
                0x00000025       0x25 obj/start.o
 .ARM.attributes
                0x0000004a       0x27 obj/portASM.o
 .ARM.attributes
                0x00000071       0x37 obj/main.o
 .ARM.attributes
                0x000000a8       0x37 obj/BSPPrint.o
 .ARM.attributes
                0x000000df       0x37 obj/gic_handle_irq.o
 .ARM.attributes
                0x00000116       0x37 obj/irq_gic.o
 .ARM.attributes
                0x0000014d       0x37 obj/mprintf.o
 .ARM.attributes
                0x00000184       0x37 obj/sram_on_chip_test.o
 .ARM.attributes
                0x000001bb       0x37 obj/timer.o
 .ARM.attributes
                0x000001f2       0x37 obj/list.o
 .ARM.attributes
                0x00000229       0x37 obj/tasks.o
 .ARM.attributes
                0x00000260       0x37 obj/port.o
 .ARM.attributes
                0x00000297       0x37 obj/heap_4.o
 .ARM.attributes
                0x000002ce       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .ARM.attributes
                0x000002ea       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .ARM.attributes
                0x00000306       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
 .ARM.attributes
                0x00000322       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .ARM.attributes
                0x0000034c       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .ARM.attributes
                0x00000368       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .ARM.attributes
                0x00000384       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .ARM.attributes
                0x000003ae       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .ARM.attributes
                0x000003d8       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .ARM.attributes
                0x00000402       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .ARM.attributes
                0x0000042c       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .ARM.attributes
                0x00000456       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .ARM.attributes
                0x00000480       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .ARM.attributes
                0x000004aa       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .ARM.attributes
                0x000004da       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .ARM.attributes
                0x00000504       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .ARM.attributes
                0x0000052e       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .ARM.attributes
                0x00000558       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .ARM.attributes
                0x00000582       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .ARM.attributes
                0x000005ac       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .ARM.attributes
                0x000005d6       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .ARM.attributes
                0x00000600       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .ARM.attributes
                0x0000062a       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .ARM.attributes
                0x00000654       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .ARM.attributes
                0x0000067e       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .ARM.attributes
                0x000006a8       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .ARM.attributes
                0x000006d2       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .ARM.attributes
                0x000006fc       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .ARM.attributes
                0x00000726       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .ARM.attributes
                0x00000750       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .ARM.attributes
                0x0000077a       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .ARM.attributes
                0x000007a4       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .ARM.attributes
                0x000007d4       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .ARM.attributes
                0x000007f0       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .ARM.attributes
                0x0000080c       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .ARM.attributes
                0x00000828       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .ARM.attributes
                0x00000844       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .ARM.attributes
                0x00000860       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .ARM.attributes
                0x0000088a       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
 .ARM.attributes
                0x000008a6       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
 .ARM.attributes
                0x000008c2       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
OUTPUT(soc2018_freertos.elf elf32-littlearm)
