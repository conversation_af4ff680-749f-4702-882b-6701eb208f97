//
// Base addresses for external devices
//

#ifndef SOC502_PARAMETER_H
#define SOC502_PARAMETER_H

#define TUBE_BASE                 0x4fff0000

#define SDRAM_REG_BASE            0x21000000

#define A9_TIMER_BASE            0x3FFF0000 
#define A9_PRIVATE_BASE           (A9_TIMER_BASE + 0x0600)
#define A9_GLOBAL_TIMER_BASE      (A9_TIMER_BASE + 0x0200)

#define APB_UART0_BASE            0xA0000000
#define APB_UART1_BASE            0xA0000100
#define APB_UART2_BASE            0xA0000200
#define APB_UART3_BASE            0xA0000300

#define EMIF_CS0_BASE             0x00000000
#define EMIF_CS1_BASE             0x04000000
#define EMIF_CS2_BASE             0x08000000
#define EMIF_CS3_BASE             0x0C000000
#define EMIF_CS4_BASE             0x10000000
#define EMIF_CS5_BASE             0x14000000
#define EMIF_CS6_BASE             0x18000000
#define EMIF_CS7_BASE             0x1C000000
#define EMIF_REG_BASE             0x20000000

#define EMIF_REG0_ADDR            (EMIF_REG_BASE + 0x0)
#define EMIF_REG1_ADDR            (EMIF_REG_BASE + 0x4)
#define EMIF_REG2_ADDR            (EMIF_REG_BASE + 0x8)
#define EMIF_REG3_ADDR            (EMIF_REG_BASE + 0xC)
#define EMIF_REG4_ADDR            (EMIF_REG_BASE + 0x10)
#define EMIF_REG5_ADDR            (EMIF_REG_BASE + 0x14)
#define EMIF_REG6_ADDR            (EMIF_REG_BASE + 0x18)
#define EMIF_REG7_ADDR            (EMIF_REG_BASE + 0x1C)

#define AXI_SRAM_BASE      0x40000000

/*CONSTANT DEFINITION*/
#define RCV_PKG_LEN                 256
#define RTN_PKG_LEN                 256

/*STRUCT*/
typedef struct rcvpkgFrameType
{
    unsigned int packageLen;
    unsigned int testType;
    unsigned int testChannel;
    unsigned int data[RCV_PKG_LEN];     
}rcvpkgFrameType;

typedef union rcvPkgType
{
    rcvpkgFrameType rcvpkg;
    unsigned int byte[RCV_PKG_LEN+3];
}rcvPkgType;


typedef struct rtnpkgFrameType
{
    unsigned int packageLen;
    unsigned int testType;
    unsigned int testChannel;
    unsigned int data[RTN_PKG_LEN];     
}rtnpkgFrameType;

typedef union rtnPkgType
{
    rtnpkgFrameType rtnpkg;
    unsigned int byte[RTN_PKG_LEN+3];
}rtnPkgType;

//协议�?volatile rcvPkgType rcvCmmd;        // 监控串口的接收数据区（received command�?volatile rtnPkgType rtnResult;      // 返回测试结果数据�?
unsigned int error_temp;//,single_errbit_cnt,double_errbit_cnt

#endif /* EM8302_PARAMETER_H */

